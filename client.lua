-- Bigger Hitbox Script for FiveM with ox_lib menu
-- This script makes OTHER players have bigger hitboxes for the player who enables it

local enabled = false
local hitboxSize = 2.0
local debugMode = false

-- Configuration
local Config = {
    defaultHitboxSize = 2.0,    -- Default multiplier for hitbox size
    maxHitboxSize = 5.0,        -- Maximum allowed hitbox size
    minHitboxSize = 0.5         -- Minimum allowed hitbox size
}

-- Function to set player hitbox size
function SetPlayerHitbox(playerId, multiplier)
    local playerPed = GetPlayerPed(playerId)
    if DoesEntityExist(playerPed) and playerPed ~= 0 then
        -- Set the entity's collision sphere radius
        SetEntityCollisionSphere(playerPed, 0.0, 0.0, 0.0, multiplier)

        -- Modify the ped's capsule radius for better hit detection
        SetPedCapsule(playerPed, multiplier)

        -- Additional collision modifications for better hit detection
        SetEntityCollisionDisabled(playerPed, false)
        SetEntityCompletelyDisableCollision(playerPed, false, false)

        if debugMode then
            print(string.format("Set hitbox for player %d (ped: %d) to %.1fx", playerId, playerPed, multiplier))
        end
        return true
    else
        if debugMode then
            print(string.format("Failed to set hitbox for player %d - invalid ped", playerId))
        end
        return false
    end
end

-- Function to apply hitbox changes to OTHER players (not self)
function ApplyHitboxToOtherPlayers()
    if not enabled then return end

    local myPlayerId = PlayerId()
    local myPed = PlayerPedId()
    local players = GetActivePlayers()
    local successCount = 0
    local totalPlayers = 0

    for _, playerId in ipairs(players) do
        if playerId ~= myPlayerId then -- Only apply to OTHER players
            totalPlayers = totalPlayers + 1
            local success = SetPlayerHitbox(playerId, hitboxSize)
            if success then
                successCount = successCount + 1
            end
        end
    end

    if debugMode then
        print(string.format("Applied hitboxes to %d/%d other players", successCount, totalPlayers))
    end
end

-- Function to reset all other players' hitboxes to normal
function ResetOtherPlayersHitboxes()
    local myPlayerId = PlayerId()
    local players = GetActivePlayers()
    local resetCount = 0

    for _, playerId in ipairs(players) do
        if playerId ~= myPlayerId then -- Only reset OTHER players
            local success = SetPlayerHitbox(playerId, 1.0)
            if success then
                resetCount = resetCount + 1
            end
        end
    end

    if debugMode then
        print(string.format("Reset hitboxes for %d other players", resetCount))
    end
end

-- Main thread
Citizen.CreateThread(function()
    while true do
        if enabled then
            ApplyHitboxToOtherPlayers()
        end
        Citizen.Wait(1000) -- Check every second
    end
end)

-- Handle player respawning
AddEventHandler('playerSpawned', function()
    Citizen.Wait(1000) -- Wait a bit for the player to fully spawn
    if enabled then
        ApplyHitboxToOtherPlayers()
    end
end)

-- Handle new players joining
AddEventHandler('onClientResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        Citizen.Wait(2000)
        if enabled then
            ApplyHitboxToOtherPlayers()
        end
    end
end)

-- Function to open the hitbox menu
function OpenHitboxMenu()
    local options = {
        {
            title = 'Toggle Hitboxes',
            description = enabled and 'Disable bigger hitboxes for other players' or 'Enable bigger hitboxes for other players',
            icon = enabled and 'toggle-off' or 'toggle-on',
            onSelect = function()
                enabled = not enabled
                local status = enabled and "enabled" or "disabled"

                if enabled then
                    ApplyHitboxToOtherPlayers()
                    lib.notify({
                        title = 'Hitbox System',
                        description = 'Other players now have bigger hitboxes for you',
                        type = 'success'
                    })
                else
                    ResetOtherPlayersHitboxes()
                    lib.notify({
                        title = 'Hitbox System',
                        description = 'Other players hitboxes reset to normal',
                        type = 'inform'
                    })
                end
                OpenHitboxMenu() -- Refresh menu
            end
        },
        {
            title = 'Hitbox Size',
            description = 'Current size: ' .. hitboxSize .. 'x',
            icon = 'expand-arrows-alt',
            onSelect = function()
                local input = lib.inputDialog('Set Hitbox Size', {
                    {
                        type = 'slider',
                        label = 'Hitbox Multiplier',
                        description = 'Size multiplier for other players hitboxes',
                        required = true,
                        default = hitboxSize,
                        min = Config.minHitboxSize,
                        max = Config.maxHitboxSize,
                        step = 0.1
                    }
                })

                if input then
                    hitboxSize = input[1]
                    if enabled then
                        ApplyHitboxToOtherPlayers()
                    end
                    lib.notify({
                        title = 'Hitbox System',
                        description = 'Hitbox size set to ' .. hitboxSize .. 'x',
                        type = 'success'
                    })
                    OpenHitboxMenu() -- Refresh menu
                end
            end
        },
        {
            title = 'Debug Mode',
            description = debugMode and 'Disable debug messages' or 'Enable debug messages',
            icon = debugMode and 'bug' or 'info-circle',
            onSelect = function()
                debugMode = not debugMode
                local status = debugMode and "enabled" or "disabled"
                lib.notify({
                    title = 'Hitbox System',
                    description = 'Debug mode ' .. status,
                    type = 'inform'
                })
                OpenHitboxMenu() -- Refresh menu
            end
        }
    }

    lib.registerContext({
        id = 'hitbox_menu',
        title = 'Hitbox Settings',
        options = options
    })

    lib.showContext('hitbox_menu')
end

-- Command to open the hitbox menu
RegisterCommand('hitbox', function(source, args, rawCommand)
    OpenHitboxMenu()
end, false)





-- Handle server events for syncing
RegisterNetEvent('hitbox:updatePlayer')
AddEventHandler('hitbox:updatePlayer', function(playerId, hitboxSize)
    SetPlayerHitbox(playerId, hitboxSize)
end)

RegisterNetEvent('hitbox:resetAll')
AddEventHandler('hitbox:resetAll', function()
    local players = GetActivePlayers()
    for _, playerId in ipairs(players) do
        SetPlayerHitbox(playerId, 1.0)
    end
    enabled = false
    TriggerEvent('chat:addMessage', {
        color = {255, 165, 0},
        multiline = true,
        args = {"[Hitbox]", "All hitboxes reset by admin"}
    })
end)

-- Initialize
Citizen.CreateThread(function()
    Citizen.Wait(2000) -- Wait for everything to load
    print("^2[Hitbox Script] ^7Loaded successfully!")
    print("^3[Hitbox Script] ^7Use /hitbox to open the settings menu")

    -- Show initial notification
    lib.notify({
        title = 'Hitbox Script',
        description = 'Use /hitbox to open settings menu',
        type = 'inform',
        duration = 5000
    })
end)
