-- Server-side script for Bigger Hitbox
-- Handles server-side logic and synchronization

local Config = {
    allowClientControl = true,  -- Allow clients to control their own hitboxes
    maxHitboxSize = 5.0,       -- Maximum allowed hitbox size
    defaultHitboxSize = 2.0,   -- Default hitbox size
    logCommands = true         -- Log hitbox commands to console
}

-- Store player hitbox settings
local playerHitboxes = {}

-- Event to sync hitbox changes across clients
RegisterServerEvent('hitbox:syncPlayer')
AddEventHandler('hitbox:syncPlayer', function(targetPlayer, hitboxSize)
    local source = source
    
    -- Validate the hitbox size
    if hitboxSize > Config.maxHitboxSize then
        hitboxSize = Config.maxHitboxSize
    elseif hitboxSize < 0.1 then
        hitboxSize = 0.1
    end
    
    -- Store the player's hitbox setting
    playerHitboxes[targetPlayer] = hitboxSize
    
    -- Sync to all clients
    TriggerClientEvent('hitbox:updatePlayer', -1, targetPlayer, hitboxSize)
    
    if Config.logCommands then
        print(string.format("[Hitbox] Player %d set hitbox size to %.1fx", source, hitboxSize))
    end
end)

-- Command to set hitbox for specific player (admin only)
RegisterCommand('sethitboxplayer', function(source, args, rawCommand)
    -- Check if player has admin permissions (you may need to adjust this based on your admin system)
    if not IsPlayerAceAllowed(source, "command.sethitboxplayer") then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"[Hitbox]", "You don't have permission to use this command!"}
        })
        return
    end
    
    if args[1] and args[2] then
        local targetId = tonumber(args[1])
        local hitboxSize = tonumber(args[2])
        
        if targetId and hitboxSize and hitboxSize > 0 and hitboxSize <= Config.maxHitboxSize then
            local targetPlayer = GetPlayerFromServerId(targetId)
            if targetPlayer then
                playerHitboxes[targetId] = hitboxSize
                TriggerClientEvent('hitbox:updatePlayer', -1, targetPlayer, hitboxSize)
                
                TriggerClientEvent('chat:addMessage', source, {
                    color = {0, 255, 0},
                    multiline = true,
                    args = {"[Hitbox]", string.format("Set player %d hitbox to %.1fx", targetId, hitboxSize)}
                })
                
                if Config.logCommands then
                    print(string.format("[Hitbox] Admin %d set player %d hitbox to %.1fx", source, targetId, hitboxSize))
                end
            else
                TriggerClientEvent('chat:addMessage', source, {
                    color = {255, 0, 0},
                    multiline = true,
                    args = {"[Hitbox]", "Player not found!"}
                })
            end
        else
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 255, 0},
                multiline = true,
                args = {"[Hitbox]", string.format("Usage: /sethitboxplayer [playerid] [size] (max: %.1f)", Config.maxHitboxSize)}
            })
        end
    else
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 255, 0},
            multiline = true,
            args = {"[Hitbox]", "Usage: /sethitboxplayer [playerid] [size]"}
        })
    end
end, true) -- Restrict to server console and admins

-- Reset all hitboxes command (admin only)
RegisterCommand('resethitboxes', function(source, args, rawCommand)
    if not IsPlayerAceAllowed(source, "command.resethitboxes") then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"[Hitbox]", "You don't have permission to use this command!"}
        })
        return
    end
    
    -- Reset all stored hitboxes
    playerHitboxes = {}
    
    -- Tell all clients to reset hitboxes
    TriggerClientEvent('hitbox:resetAll', -1)
    
    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 255, 0},
        multiline = true,
        args = {"[Hitbox]", "All hitboxes have been reset to normal size"}
    })
    
    if Config.logCommands then
        print(string.format("[Hitbox] Admin %d reset all hitboxes", source))
    end
end, true)

-- When a player joins, send them the current hitbox states
AddEventHandler('playerJoining', function()
    local source = source
    Citizen.Wait(5000) -- Wait for player to fully load
    
    -- Send current hitbox settings to the new player
    for playerId, hitboxSize in pairs(playerHitboxes) do
        local player = GetPlayerFromServerId(playerId)
        if player then
            TriggerClientEvent('hitbox:updatePlayer', source, player, hitboxSize)
        end
    end
end)

-- Clean up when player leaves
AddEventHandler('playerDropped', function(reason)
    local source = source
    playerHitboxes[source] = nil
end)

-- Server startup message
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print("^2[Hitbox Script] ^7Server-side loaded successfully!")
        print("^3[Hitbox Script] ^7Available admin commands:")
        print("^7  /sethitboxplayer [id] [size] - Set specific player's hitbox")
        print("^7  /resethitboxes - Reset all hitboxes to normal")
    end
end)
