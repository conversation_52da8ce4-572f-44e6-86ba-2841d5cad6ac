# Bigger Hitbox Script for FiveM with ox_lib Menu

A FiveM resource that makes OTHER players have bigger hitboxes for the player who enables it, making it easier to hit them in combat scenarios.

## Features

- **ox_lib Menu Interface**: Clean and intuitive menu system
- **Individual Player Control**: Each player sees other players with bigger hitboxes independently
- **Adjustable Hitbox Size**: Customize hitbox multiplier from 0.5x to 5.0x with slider
- **Toggle On/Off**: Enable or disable bigger hitboxes for other players
- **Admin Controls**: Server admins can control individual player hitboxes
- **Real-time Updates**: Changes apply immediately without restart
- **Debug Mode**: Optional debug information for troubleshooting

## Installation

1. **Install ox_lib**: Make sure you have ox_lib installed on your server
2. Download or clone this repository
3. Place the folder in your FiveM server's `resources` directory
4. Add `ensure ox_lib` and `ensure bigger-hitbox` to your `server.cfg`
5. Restart your server or use `refresh` and `start bigger-hitbox`

## Commands

### Client Commands
- `/hitbox` - Opens the ox_lib menu with all hitbox settings

### Admin Commands (Server)
- `/sethitboxplayer [playerid] [size]` - Set specific player's hitbox size
- `/resethitboxes` - Reset all hitboxes to normal size

## Menu Options

The `/hitbox` command opens a menu with the following options:

1. **Toggle Hitboxes**: Enable/disable bigger hitboxes for other players
2. **Hitbox Size**: Adjust the size multiplier using a slider (0.5x to 5.0x)
3. **Debug Mode**: Toggle debug messages on/off

## Configuration

Edit the `Config` table in `client.lua` to customize:

```lua
local Config = {
    defaultHitboxSize = 2.0,    -- Default multiplier for hitbox size
    maxHitboxSize = 5.0,        -- Maximum allowed hitbox size
    minHitboxSize = 0.5         -- Minimum allowed hitbox size
}
```

Edit the server config in `server.lua`:

```lua
local Config = {
    allowClientControl = true,  -- Allow clients to control their own hitboxes
    maxHitboxSize = 5.0,       -- Maximum allowed hitbox size
    defaultHitboxSize = 2.0,   -- Default hitbox size
    logCommands = true         -- Log hitbox commands to console
}
```

## Permissions

For admin commands, make sure to set up proper ACE permissions in your `server.cfg`:

```
add_ace group.admin command.sethitboxplayer allow
add_ace group.admin command.resethitboxes allow
```

## How It Works

When a player enables hitboxes through the menu, the script modifies OTHER players' entity collision spheres and ped capsules to create larger hit detection areas **for that specific player only**. This means:

- **Player A** enables hitboxes → Player A sees all other players with bigger hitboxes
- **Player B** doesn't enable hitboxes → Player B sees everyone with normal hitboxes
- **Player A's own hitbox remains normal** - only other players appear bigger to them

This is useful for:
- Training servers
- PvP events with different skill levels
- Roleplay scenarios requiring easier combat
- Accessibility improvements
- Individual player preferences without affecting others

## Notes

- **Requires ox_lib** - Make sure ox_lib is installed and started before this resource
- Each player sees other players with bigger hitboxes independently
- Your own hitbox remains normal - only other players appear bigger to you
- Hitbox changes are visual/collision-based and don't affect actual player model size
- Changes are applied continuously to handle respawning and new players joining
- Server-side validation prevents abuse of hitbox sizes
- Compatible with most other FiveM resources

## Troubleshooting

1. **Script not working**: Check console for errors and ensure resource is started
2. **Commands not responding**: Verify permissions are set correctly
3. **Hitboxes not applying**: Try toggling debug mode to see if changes are being applied
4. **Performance issues**: Increase the wait time in the main thread if needed

## Support

If you encounter issues or need help customizing the script, please check the console for error messages and ensure all files are properly installed.
